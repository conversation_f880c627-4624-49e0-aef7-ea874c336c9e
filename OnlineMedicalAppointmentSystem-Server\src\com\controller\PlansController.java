package com.controller;

import com.model.*;
import com.response.Response;
import com.service.*;
import com.util.PageBean;
import com.util.removeHTML;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.io.FileWriter;
import java.io.IOException;

@RestController
@RequestMapping("/api/plans")
public class PlansController{
	
	@Resource
	private PlansService plansService;

	@Resource
	private ReserveService reserveService;
	
	// 写入日志文件的方法
	private void writeLog(String message) {
		try {
			FileWriter fw = new FileWriter("plans_debug.log", true);
			fw.write(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + " - " + message + "\n");
			fw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	//坐诊安排列表
	@RequestMapping(value="/list")
	@CrossOrigin
	public Response<List<Plans>> list(@RequestBody Plans plans, @RequestParam Integer currentPage, HttpServletRequest req) throws Exception {
		int pageSize=Integer.parseInt(req.getParameter("pageSize")); //每页显示记录数
		int offset = (currentPage - 1) * pageSize; //当前页开始记录
		int counts = 0;  //总记录数
		PageBean page = new PageBean(offset, pageSize); //分页对象
		//查询记录总数
		counts = plansService.getCount(plans);
		//获取当前页记录
		List<Plans> plansList = plansService.queryPlansList(plans, page);
        
		int page_count = counts % PageBean.PAGE_IETM == 0 ? counts / PageBean.PAGE_IETM : counts / PageBean.PAGE_IETM + 1; //总页数
		return Response.success(plansList, counts, page_count);
	}

		//坐诊安排列表
	@RequestMapping(value="/list2")
	@CrossOrigin
	public Response<List<Plans>> list2( Plans plans, @RequestParam Integer currentPage, HttpServletRequest req) throws Exception {
		// 测试控制台输出
		String startMsg = "PlansController.list2 方法开始执行，时间: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

		
		System.out.println("=== PlansController.list2 方法被调用 ===");
		System.out.println("请求参数: currentPage=" + currentPage + ", pageSize=" + req.getParameter("pageSize"));
		writeLog("请求参数: currentPage=" + currentPage + ", pageSize=" + req.getParameter("pageSize"));
		
		int pageSize=Integer.parseInt(req.getParameter("pageSize")); //每页显示记录数
		int offset = (currentPage - 1) * pageSize; //当前页开始记录
		int counts = 0;  //总记录数
		PageBean page = new PageBean(offset, pageSize); //分页对象
		//查询记录总数
		counts = plansService.getCount(plans);
		//获取当前页记录
		List<Plans> plansList = plansService.queryPlansList(plans, page);

		String countMsg = "查询到排班记录数: " + (plansList != null ? plansList.size() : 0);
		System.out.println(countMsg);
		writeLog(countMsg);

		if (plansList != null && !plansList.isEmpty()) {
			for (Plans plan : plansList) {
				// 查询该排班的预约记录数
				Reserve reserve = new Reserve();
				reserve.setPlid(plan.getPlid());
				List<Reserve> reserves = reserveService.queryReserveList(reserve, null);

				// 设置已预约人数
				int yylatenum = reserves != null ? reserves.size() : 0;
				plan.setYylatenum(yylatenum);

				// 打印调试信息
				String planMsg = "排班信息: plid=" + plan.getPlid()
						+ ", 总号数=" + plan.getPeople()
						+ ", 已预约=" + yylatenum
						+ ", 剩余=" + (plan.getPeople() - yylatenum);
				System.out.println(planMsg);
				writeLog(planMsg);
			}
		} else {
			String noDataMsg = "没有查询到排班数据";
			System.out.println(noDataMsg);
			writeLog(noDataMsg);
		}
        
		int page_count = counts % PageBean.PAGE_IETM == 0 ? counts / PageBean.PAGE_IETM : counts / PageBean.PAGE_IETM + 1; //总页数
		
		String endMsg = "PlansController.list2 方法执行完毕";
		System.out.println("=== " + endMsg + " ===");
		System.out.println("==========================================");
		writeLog(endMsg);
		return Response.success(plansList, counts, page_count);
	}
        
	//添加坐诊安排
	@ResponseBody
	@PostMapping(value = "/add")
	@CrossOrigin
	public Response add(@RequestBody Plans plans, HttpServletRequest req) throws Exception {
		try {
			plansService.insertPlans(plans); //添加
   
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
    
	//删除坐诊安排
	@ResponseBody
	@PostMapping(value = "/del")
	@CrossOrigin
	public Response del(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			plansService.deletePlans(id); //删除
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//修改坐诊安排
	@ResponseBody
	@PostMapping(value = "/update")
	@CrossOrigin
	public Response update(@RequestBody Plans plans, HttpServletRequest req) throws Exception {
		try {
			plansService.updatePlans(plans); //修改
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//返回坐诊安排详情
	@ResponseBody
	@PostMapping(value = "/get")
	@CrossOrigin
	public Response get(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			Plans plans=plansService.queryPlansById(id); //根据ID查询
			return Response.success(plans);
			} catch (Exception e) {
			return Response.error();
		}
       
	}
    
}

