﻿<template>
  <div style="width: 100%; padding: 20px;" id="home">
    <!-- 欢迎信息 -->
    <div style="text-align: center; margin-bottom: 30px;">
      <h2 style="color: #409EFF; margin-bottom: 10px;">欢迎使用医院挂号预约系统</h2>
      <p style="font-size: 16px; color: #666;">
        账号：<b style="color: #E6A23C;">{{ userLname }}</b>，
        身份：<b style="color: #E6A23C;">{{ role }}</b>
      </p>
    </div>

    <!-- 统计卡片 -->
    <div v-loading="loading" style="display: flex; flex-wrap: wrap; gap: 20px; justify-content: center;">
      <!-- 管理员统计卡片 -->
      <template v-if="role === '管理员'">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon patients">
              <i class="el-icon-user"></i>
            </div>
            <div class="stat-info">
              <h3>{{ statistics.patientCount || 0 }}</h3>
              <p>患者总数</p>
            </div>
          </div>
        </el-card>

        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon doctors">
              <i class="el-icon-user-solid"></i>
            </div>
            <div class="stat-info">
              <h3>{{ statistics.doctorCount || 0 }}</h3>
              <p>医生总数</p>
            </div>
          </div>
        </el-card>

        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon today-appointments">
              <i class="el-icon-date"></i>
            </div>
            <div class="stat-info">
              <h3>{{ statistics.todayAppointmentCount || 0 }}</h3>
              <p>今日挂号数</p>
            </div>
          </div>
        </el-card>

        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon total-appointments">
              <i class="el-icon-document"></i>
            </div>
            <div class="stat-info">
              <h3>{{ statistics.totalAppointmentCount || 0 }}</h3>
              <p>总挂号数</p>
            </div>
          </div>
        </el-card>
      </template>

      <!-- 医生统计卡片 -->
      <template v-if="role === '医生'">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon today-appointments">
              <i class="el-icon-date"></i>
            </div>
            <div class="stat-info">
              <h3>{{ statistics.doctorTodayAppointmentCount || 0 }}</h3>
              <p>我的今日挂号数</p>
            </div>
          </div>
        </el-card>

        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon total-appointments">
              <i class="el-icon-document"></i>
            </div>
            <div class="stat-info">
              <h3>{{ statistics.doctorTotalAppointmentCount || 0 }}</h3>
              <p>我的总挂号数</p>
            </div>
          </div>
        </el-card>
      </template>
    </div>

    <!-- 操作提示 -->
    <div style="text-align: center; margin-top: 40px; color: #909399;">
      <p style="font-size: 14px;">请在左侧菜单中选择您要进行的操作！</p>
    </div>
  </div>
</template>

<script>
import request, { base } from "../../../utils/http";

export default {
  data() {
    return {
      userLname: "",
      role: "",
      loading: false,
      statistics: {
        patientCount: 0,
        doctorCount: 0,
        todayAppointmentCount: 0,
        totalAppointmentCount: 0,
        doctorTodayAppointmentCount: 0,
        doctorTotalAppointmentCount: 0
      }
    };
  },
  mounted() {
    this.userLname = sessionStorage.getItem("userLname");
    this.role = sessionStorage.getItem("role");
    this.loadStatistics();
  },
  methods: {
    // 加载统计数据
    async loadStatistics() {
      this.loading = true;
      try {
        const user = JSON.parse(sessionStorage.getItem("user"));
        let url = base + "/statistics/dashboard";

        const params = {
          role: this.role
        };

        // 如果是医生，传递医生ID
        if (this.role === '医生' && user) {
          params.doctorId = user.did;
        }

        const response = await request.post(url, params);
        if (response.code === 200) {
          this.statistics = response.resdata;
        } else {
          this.$message.error('获取统计数据失败');
        }
      } catch (error) {
        console.error('加载统计数据失败:', error);
        // 如果后端服务器未启动，显示模拟数据用于演示
        if (this.role === '管理员') {
          this.statistics = {
            patientCount: 156,
            doctorCount: 23,
            todayAppointmentCount: 45,
            totalAppointmentCount: 1234
          };
        } else if (this.role === '医生') {
         
        }
     
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>

<style scoped>
.stat-card {
  width: 230px;
  margin-bottom: 20px;
  border-radius: 8px;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.stat-icon.patients {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.doctors {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.today-appointments {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.total-appointments {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info h3 {
  font-size: 32px;
  font-weight: bold;
  margin: 0;
  color: #303133;
}

.stat-info p {
  font-size: 14px;
  color: #909399;
  margin: 5px 0 0 0;
}

@media (max-width: 768px) {
  .stat-card {
    width: 100%;
    max-width: 300px;
  }
}
</style>

