package com.controller;

import com.response.Response;
import com.service.StatisticsService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/statistics")
public class StatisticsController {

    @Resource
    private StatisticsService statisticsService;

    /**
     * 获取仪表板统计数据
     */
    @ResponseBody
    @PostMapping(value = "/dashboard")
    @CrossOrigin
    public Response getDashboardStatistics(@RequestBody Map<String, Object> params, HttpServletRequest req) throws Exception {
        try {
            String role = (String) params.get("role");
            Map<String, Object> statistics = new HashMap<>();

            if ("管理员".equals(role)) {
                // 管理员统计数据
                statistics.put("patientCount", statisticsService.getPatientCount());
                statistics.put("doctorCount", statisticsService.getDoctorCount());
                statistics.put("todayAppointmentCount", statisticsService.getTodayAppointmentCount());
                statistics.put("totalAppointmentCount", statisticsService.getTotalAppointmentCount());
            } else if ("医生".equals(role)) {
                // 医生统计数据
                Object doctorIdObj = params.get("doctorId");
                System.out.println("医生统计请求 - doctorIdObj: " + doctorIdObj + ", 类型: " + (doctorIdObj != null ? doctorIdObj.getClass().getSimpleName() : "null"));
                
                if (doctorIdObj != null) {
                    Integer doctorId;
                    // 直接处理，因为前端传来的user.did是整数
                    if (doctorIdObj instanceof Integer) {
                        doctorId = (Integer) doctorIdObj;
                    } else {
                        // 处理JSON传输中可能的字符串情况
                       
                        //强制转换
                        doctorId = Integer.parseInt(doctorIdObj.toString());
                        
                    }
                    
                    System.out.println("解析后的医生ID: " + doctorId);
                    
                    statistics.put("doctorTodayAppointmentCount", 
                        statisticsService.getDoctorTodayAppointmentCount(doctorId));
                    statistics.put("doctorTotalAppointmentCount", 
                        statisticsService.getDoctorTotalAppointmentCount(doctorId));
                        
                    System.out.println("医生统计数据查询完成");
                }
            }

            return Response.success(statistics);
        } catch (Exception e) {
            e.printStackTrace();
            return Response.error();
        }
    }
}
